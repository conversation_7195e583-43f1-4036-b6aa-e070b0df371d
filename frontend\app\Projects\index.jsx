import React, { useContext, useRef, useState } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StatusBar,
    SafeAreaView,
    RefreshControl,
    Image,
    Animated,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import { useQuery } from '@tanstack/react-query';
import { fetchUserProjects } from '../../api/projects/projectApi';
import { showToast } from '../../utils/showToast';
import BackButton from '../Components/Shared/BackButton';
import { styles } from './styles';

export default function ProjectsIndex() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const [refreshing, setRefreshing] = useState(false);
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;

    const {
        data: projectsData,
        isLoading,
        error,
        refetch,
        isRefetching,
    } = useQuery({
        queryKey: ['userProjects'],
        queryFn: fetchUserProjects,
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to fetch projects'
            );
        },
    });

    React.useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const projects = projectsData?.projects || [];

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Budget not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Budget not specified';
    };

    const ProjectCard = ({ project, index }) => (
        <Animated.View
            style={{
                opacity: fadeAnim,
                transform: [
                    {
                        translateY: slideAnim.interpolate({
                            inputRange: [0, 50],
                            outputRange: [0, 50 * (index + 1)],
                        }),
                    },
                ],
            }}
        >
            <TouchableOpacity
                style={[
                    combinedStyles.card,
                    {
                        backgroundColor: theme.CARD,
                        shadowColor: theme.SHADOW,
                        borderColor: theme.BORDER,
                    },
                ]}
                onPress={() =>
                    router.push(`/Projects/ProjectDetails?id=${project._id}`)
                }
                activeOpacity={0.8}
            >
                <View style={combinedStyles.cardHeader}>
                    <View style={combinedStyles.projectInfo}>
                        <Text
                            style={[
                                combinedStyles.cardTitle,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.projectName}
                        </Text>
                        <Text
                            style={[
                                combinedStyles.projectType,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {project.projectType || 'Not specified'} • {project.constructionType || 'Not specified'}
                        </Text>
                    </View>
                    <View style={combinedStyles.badgeContainer}>
                        {/* Role Badge */}
                        {project.userRoleInProject && project.userRoleInProject !== 'owner' && (
                            <View
                                style={[
                                    combinedStyles.roleBadge,
                                    {
                                        backgroundColor: project.userRoleInProject === 'contractor'
                                            ? theme.SUCCESS + '20'
                                            : theme.WARNING + '20'
                                    },
                                ]}
                            >
                                <Text style={[
                                    combinedStyles.roleText,
                                    {
                                        color: project.userRoleInProject === 'contractor'
                                            ? theme.SUCCESS
                                            : theme.WARNING
                                    }
                                ]}>
                                    {project.userRoleInProject === 'contractor' ? 'Hired as Contractor' : 'Hired as Broker'}
                                </Text>
                            </View>
                        )}
                        <View
                            style={[
                                combinedStyles.statusBadge,
                                { backgroundColor: theme.PRIMARY + '20' },
                            ]}
                        >
                            <Text style={[combinedStyles.statusText, { color: theme.PRIMARY }]}>
                                Active
                            </Text>
                        </View>
                    </View>
                </View>

                <View style={combinedStyles.cardContent}>
                    <View style={combinedStyles.detailRow}>
                        <MaterialIcons
                            name="location-on"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                combinedStyles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.location?.city}, {project.location?.state}
                        </Text>
                    </View>

                    <View style={combinedStyles.detailRow}>
                        <MaterialIcons
                            name="attach-money"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                combinedStyles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {formatBudget(project.budget)}
                        </Text>
                    </View>

                    {project.designPreferences && (
                        <View style={combinedStyles.detailRow}>
                            <MaterialIcons
                                name="home"
                                size={16}
                                color={theme.TEXT_SECONDARY}
                            />
                            <Text
                                style={[
                                    combinedStyles.detailText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                {project.designPreferences.floors &&
                                    `${project.designPreferences.floors} Floors`}
                                {project.designPreferences.bedrooms &&
                                    ` • ${project.designPreferences.bedrooms} BHK`}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={combinedStyles.cardFooter}>
                    <View style={combinedStyles.teamInfo}>
                        {project.brokerId && (
                            <View style={combinedStyles.teamMember}>
                                <MaterialIcons
                                    name="business"
                                    size={14}
                                    color={theme.PRIMARY}
                                />
                                <Text
                                    style={[
                                        combinedStyles.teamText,
                                        { color: theme.PRIMARY },
                                    ]}
                                >
                                    Broker Hired
                                </Text>
                            </View>
                        )}
                        {project.contractorId && (
                            <View style={combinedStyles.teamMember}>
                                <MaterialIcons
                                    name="build"
                                    size={14}
                                    color={theme.SUCCESS}
                                />
                                <Text
                                    style={[
                                        combinedStyles.teamText,
                                        { color: theme.SUCCESS },
                                    ]}
                                >
                                    Contractor Hired
                                </Text>
                            </View>
                        )}
                        {!project.brokerId && !project.contractorId && (
                            <Text
                                style={[
                                    combinedStyles.noTeamText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                No professionals hired yet
                            </Text>
                        )}
                    </View>
                    <Ionicons
                        name="chevron-forward"
                        size={20}
                        color={theme.TEXT_SECONDARY}
                    />
                </View>
            </TouchableOpacity>
        </Animated.View>
    );

    const EmptyState = () => (
        <View style={combinedStyles.emptyContainer}>
            <MaterialIcons
                name="assignment"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[combinedStyles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Projects Yet
            </Text>
            <Text
                style={[combinedStyles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                Create your first project to get started with finding the right
                professionals
            </Text>
            <TouchableOpacity
                style={combinedStyles.createButton}
                onPress={() => router.push('/Projects/CreateProject')}
                activeOpacity={0.8}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={combinedStyles.gradient}
                />
                <Text style={[combinedStyles.createButtonText, { color: theme.WHITE }]}>
                    Create Project
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <View style={[combinedStyles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            {/* Background Gradient */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                start={{ x: 0, y: 0.2 }}
                end={{ x: 0.9, y: 0.4 }}
                style={combinedStyles.backgroundContainer}
            />

            <BackButton color={theme.WHITE} />

            {/* Header */}
            <View style={[combinedStyles.header, { backgroundColor: 'transparent' }]}>
                <Text
                    style={[combinedStyles.headerTitle, { color: theme.WHITE }]}
                >
                    My Projects
                </Text>
                <TouchableOpacity
                    onPress={() => router.push('/Projects/CreateProject')}
                    style={[
                        combinedStyles.addButton,
                        {
                            backgroundColor: theme.WHITE + '20',
                            borderColor: theme.WHITE,
                            borderWidth: 1,
                        },
                    ]}
                    activeOpacity={0.8}
                >
                    <Ionicons name="add" size={24} color={theme.WHITE} />
                </TouchableOpacity>
            </View>

            <ScrollView
                style={combinedStyles.scrollContainer}
                contentContainerStyle={combinedStyles.scrollContent}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefetching}
                        onRefresh={refetch}
                        colors={[theme.PRIMARY]}
                        tintColor={theme.WHITE}
                    />
                }
            >
                <Animated.View
                    style={[
                        combinedStyles.contentContainer,
                        {
                            opacity: fadeAnim,
                            transform: [{ translateY: slideAnim }],
                        },
                    ]}
                >
                    {isLoading ? (
                        <View style={combinedStyles.loadingContainer}>
                            <Text
                                style={[
                                    combinedStyles.loadingText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Loading projects...
                            </Text>
                        </View>
                    ) : projects.length === 0 ? (
                        <EmptyState />
                    ) : (
                        <View style={combinedStyles.projectsList}>
                            {projects.map((project, index) => (
                                <ProjectCard
                                    key={project._id}
                                    project={project}
                                    index={index}
                                />
                            ))}
                        </View>
                    )}
                </Animated.View>
            </ScrollView>
        </View>
    );
}

// Additional styles specific to Projects index
const localStyles = {
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 16,
        marginTop: 20,
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: 'bold',
    },
    addButton: {
        width: 44,
        height: 44,
        borderRadius: 22,
        alignItems: 'center',
        justifyContent: 'center',
    },
    scrollContent: {
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    projectsList: {
        gap: 16,
    },
    projectInfo: {
        flex: 1,
        marginRight: 12,
    },
    projectType: {
        fontSize: 14,
        marginTop: 4,
    },
    badgeContainer: {
        alignItems: 'flex-end',
        gap: 4,
    },
    roleBadge: {
        paddingHorizontal: 8,
        paddingVertical: 3,
        borderRadius: 10,
    },
    roleText: {
        fontSize: 10,
        fontWeight: '600',
    },
    statusBadge: {
        paddingHorizontal: 8,
        paddingVertical: 4,
        borderRadius: 12,
    },
    statusText: {
        fontSize: 12,
        fontWeight: '500',
    },
    detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
    },
    detailText: {
        fontSize: 14,
        flex: 1,
    },
    cardFooter: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    teamInfo: {
        flex: 1,
        flexDirection: 'row',
        gap: 12,
    },
    teamMember: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    teamText: {
        fontSize: 12,
        fontWeight: '500',
    },
    noTeamText: {
        fontSize: 12,
        fontStyle: 'italic',
    },
    createButton: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    createButtonText: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 32,
    },
};

// Merge imported styles with local styles
const combinedStyles = { ...styles, ...localStyles };
