# BUILD-CONNECT MongoDB Sample Data

This directory contains realistic sample data for the BUILD-CONNECT application database. The data includes comprehensive information for a construction and real estate platform with multiple user types and interconnected services.

## 📊 Data Overview

### Collections and Record Counts:
- **Users**: 10 records (Property owners, Contractors, Brokers, Buyers, Site scouts)
- **Properties**: 5 records (Residential, Commercial, Agricultural, Industrial properties)
- **Projects**: 2 records (Villa construction, Office complex)
- **Service Requests**: 5 records (Construction, Property search, Interior design, etc.)
- **Chats**: 5 records (Direct and group conversations)
- **Messages**: 12 records (Text, image, document messages)
- **Progress Logs**: 6 records (Construction progress tracking)
- **Payments**: 7 records (Milestone payments, service fees)
- **Notifications**: 10 records (Project updates, payment reminders, etc.)

## 🗂️ File Structure

```
mongodb-sample-data/
├── users.json              # User profiles with different roles
├── properties.json         # Property listings with detailed information
├── projects.json          # Construction projects with specifications
├── service_requests.json  # Service requests between users
├── chats.json             # Chat conversations
├── messages.json          # Individual messages in chats
├── progress_logs.json     # Project progress tracking
├── payments.json          # Payment transactions
├── notifications.json     # User notifications
└── README.md              # This file
```

## 🚀 How to Import Data into MongoDB Compass

### Prerequisites:
1. MongoDB installed and running
2. MongoDB Compass installed
3. A database created (suggested name: `build_connect`)

### Step-by-Step Import Process:

#### 1. Connect to MongoDB
- Open MongoDB Compass
- Connect to your MongoDB instance (usually `mongodb://localhost:27017`)

#### 2. Create Database
- Click "Create Database"
- Database Name: `build_connect`
- Collection Name: `users` (we'll create others later)

#### 3. Import Each Collection

For each JSON file, follow these steps:

**Method 1: Using MongoDB Compass GUI**
1. Select your database (`build_connect`)
2. Click "Create Collection" or select existing collection
3. Click "ADD DATA" → "Import JSON or CSV file"
4. Select the corresponding JSON file
5. Choose "JSON" as file type
6. Click "Import"

**Method 2: Using MongoDB Shell (mongosh)**
```bash
# Navigate to the mongodb-sample-data directory
cd path/to/mongodb-sample-data

# Import each collection
mongoimport --db build_connect --collection users --file users.json --jsonArray
mongoimport --db build_connect --collection properties --file properties.json --jsonArray
mongoimport --db build_connect --collection projects --file projects.json --jsonArray
mongoimport --db build_connect --collection service_requests --file service_requests.json --jsonArray
mongoimport --db build_connect --collection chats --file chats.json --jsonArray
mongoimport --db build_connect --collection messages --file messages.json --jsonArray
mongoimport --db build_connect --collection progress_logs --file progress_logs.json --jsonArray
mongoimport --db build_connect --collection payments --file payments.json --jsonArray
mongoimport --db build_connect --collection notifications --file notifications.json --jsonArray
```

#### 4. Verify Import
After importing, verify the data:
- Check document count in each collection
- Browse through sample documents
- Verify relationships between collections

## 🔗 Data Relationships

The sample data includes realistic relationships between collections:

### User Relationships:
- **Rajesh Kumar** (Property Owner) → owns properties and projects
- **Priya Sharma** (Contractor) → works on construction projects
- **Amit Patel** (Broker) → handles property deals
- **Sunita Reddy** (Buyer) → looking for properties
- **Vikram Singh** (Site Scout) → verifies properties

### Project Flow:
1. **Property Listing** → **Service Request** → **Project Creation**
2. **Progress Tracking** → **Milestone Payments** → **Notifications**
3. **Chat Communication** throughout the process

### Key Connections:
- Projects linked to properties and users
- Service requests connecting different user types
- Chats facilitating communication
- Progress logs tracking project milestones
- Payments tied to project milestones
- Notifications for real-time updates

## 📋 Sample Data Features

### Realistic Indian Context:
- **Locations**: Bangalore, Mumbai, Hyderabad, Chennai, Delhi, etc.
- **Currency**: Indian Rupees (INR)
- **Documents**: Aadhaar, PAN, property documents
- **Phone Numbers**: Indian format (+91-XXXXXXXXXX)
- **Addresses**: Real Indian city locations with pincodes

### Comprehensive User Profiles:
- Multiple user roles with role-specific fields
- Document verification status
- Experience levels and ratings
- Portfolio and work history

### Detailed Property Information:
- Various property types (residential, commercial, agricultural, industrial)
- Legal documents and approvals
- Amenities and features
- Location coordinates for mapping
- Pricing and area details

### Project Management:
- Multi-phase project tracking
- Material and labor cost breakdown
- Progress monitoring with images
- Quality checks and compliance
- Team member details

### Financial Transactions:
- Milestone-based payments
- Tax calculations (GST)
- Multiple payment methods (Razorpay, Bank transfer)
- Payment proofs and receipts

## 🛠️ Customization

You can customize this data by:

1. **Modifying User Information**: Update names, contacts, locations
2. **Adjusting Property Details**: Change locations, prices, specifications
3. **Updating Project Timelines**: Modify dates to current timeframes
4. **Changing Payment Amounts**: Adjust to your preferred currency/amounts
5. **Adding More Records**: Duplicate and modify existing records

## 🔍 Useful Queries for Testing

After importing, try these MongoDB queries:

```javascript
// Find all contractors
db.users.find({role: "contractor"})

// Find properties in Bangalore
db.properties.find({"location.city": "Bangalore"})

// Find active projects
db.projects.find({status: "in_progress"})

// Find pending payments
db.payments.find({status: "pending"})

// Find unread notifications
db.notifications.find({isRead: false})

// Find messages in a specific chat
db.messages.find({chat: "64f1a2b3c4d5e6f7a8b9c4d1"})
```

## 📝 Notes

- All ObjectIds are properly formatted and consistent across relationships
- Timestamps use ISO 8601 format
- File URLs are placeholder examples (replace with actual file storage URLs)
- Phone numbers and document numbers are fictional
- Coordinates are approximate for the mentioned locations

## 🤝 Support

If you encounter any issues while importing or need to modify the data structure, refer to the MongoDB documentation or create custom data based on these templates.

---

**Happy Building with BUILD-CONNECT! 🏗️**
