[{"_id": "64f1a2b3c4d5e6f7a8b9c7d1", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "payer": "64f1a2b3c4d5e6f7a8b9c0d1", "payee": "64f1a2b3c4d5e6f7a8b9c0d2", "amount": 850000, "currency": "INR", "paymentType": "milestone", "milestone": "Foundation Completion", "description": "Payment for completed foundation work of villa construction project", "paymentMethod": "razorpay", "razorpayOrderId": "order_MN12345678", "razorpayPaymentId": "pay_AB12345678", "razorpaySignature": "signature_xyz123", "status": "completed", "transactionDate": "2024-03-01T10:30:00Z", "dueDate": "2024-03-01T00:00:00Z", "invoiceNumber": "INV-2024-001", "taxDetails": {"gst": {"rate": 18, "amount": 153000}, "totalTax": 153000}, "netAmount": 697000, "grossAmount": 850000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_001.pdf", "bankReference": "UTR123456789"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-03-01T10:00:00Z", "updatedAt": "2024-03-01T10:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d2", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "payer": "64f1a2b3c4d5e6f7a8b9c0d1", "payee": "64f1a2b3c4d5e6f7a8b9c0d2", "amount": 1200000, "currency": "INR", "paymentType": "advance", "milestone": "Structure Work Advance", "description": "Advance payment for structure work phase", "paymentMethod": "razorpay", "razorpayOrderId": "order_MN12345679", "razorpayPaymentId": "pay_AB12345679", "razorpaySignature": "signature_xyz124", "status": "completed", "transactionDate": "2024-03-05T14:15:00Z", "dueDate": "2024-03-05T00:00:00Z", "invoiceNumber": "INV-2024-002", "taxDetails": {"gst": {"rate": 18, "amount": 216000}, "totalTax": 216000}, "netAmount": 984000, "grossAmount": 1200000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_002.pdf", "bankReference": "UTR123456790"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-03-05T14:00:00Z", "updatedAt": "2024-03-05T14:15:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d3", "project": "64f1a2b3c4d5e6f7a8b9c2d2", "payer": "64f1a2b3c4d5e6f7a8b9c0d7", "payee": "64f1a2b3c4d5e6f7a8b9c0d2", "amount": 5000000, "currency": "INR", "paymentType": "advance", "milestone": "Project Initiation", "description": "Initial advance payment for office complex construction", "paymentMethod": "bank_transfer", "status": "completed", "transactionDate": "2024-01-20T11:00:00Z", "dueDate": "2024-01-20T00:00:00Z", "invoiceNumber": "INV-2024-003", "taxDetails": {"gst": {"rate": 18, "amount": 900000}, "totalTax": 900000}, "netAmount": 4100000, "grossAmount": 5000000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_003.pdf", "bankReference": "NEFT123456789"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d7", "createdAt": "2024-01-20T10:30:00Z", "updatedAt": "2024-01-20T11:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d4", "project": null, "payer": "64f1a2b3c4d5e6f7a8b9c0d4", "payee": "64f1a2b3c4d5e6f7a8b9c0d3", "amount": 50000, "currency": "INR", "paymentType": "service_fee", "milestone": "Property Search Service", "description": "Broker service fee for property search and consultation", "paymentMethod": "razorpay", "razorpayOrderId": "order_MN12345680", "razorpayPaymentId": "pay_AB12345680", "razorpaySignature": "signature_xyz125", "status": "completed", "transactionDate": "2024-01-28T16:45:00Z", "dueDate": "2024-01-28T00:00:00Z", "invoiceNumber": "INV-2024-004", "taxDetails": {"gst": {"rate": 18, "amount": 9000}, "totalTax": 9000}, "netAmount": 41000, "grossAmount": 50000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_004.pdf", "bankReference": "UTR123456791"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d4", "createdAt": "2024-01-28T16:30:00Z", "updatedAt": "2024-01-28T16:45:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d5", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "payer": "64f1a2b3c4d5e6f7a8b9c0d1", "payee": "64f1a2b3c4d5e6f7a8b9c0d2", "amount": 1150000, "currency": "INR", "paymentType": "milestone", "milestone": "Structure Work Progress", "description": "Payment for 65% completion of structure work", "paymentMethod": "razorpay", "razorpayOrderId": "order_MN12345681", "status": "pending", "transactionDate": null, "dueDate": "2024-03-20T00:00:00Z", "invoiceNumber": "INV-2024-005", "taxDetails": {"gst": {"rate": 18, "amount": 207000}, "totalTax": 207000}, "netAmount": 943000, "grossAmount": 1150000, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-03-15T18:30:00Z", "updatedAt": "2024-03-15T18:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d6", "project": null, "payer": "64f1a2b3c4d5e6f7a8b9c0d9", "payee": "64f1a2b3c4d5e6f7a8b9c0d5", "amount": 35000, "currency": "INR", "paymentType": "service_fee", "milestone": "Property Verification", "description": "Site verification service fee for commercial property in Mumbai", "paymentMethod": "razorpay", "razorpayOrderId": "order_MN12345682", "razorpayPaymentId": "pay_AB12345682", "razorpaySignature": "signature_xyz126", "status": "completed", "transactionDate": "2024-02-10T12:30:00Z", "dueDate": "2024-02-10T00:00:00Z", "invoiceNumber": "INV-2024-006", "taxDetails": {"gst": {"rate": 18, "amount": 6300}, "totalTax": 6300}, "netAmount": 28700, "grossAmount": 35000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_006.pdf", "bankReference": "UTR123456792"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d9", "createdAt": "2024-02-10T12:00:00Z", "updatedAt": "2024-02-10T12:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c7d7", "project": "64f1a2b3c4d5e6f7a8b9c2d2", "payer": "64f1a2b3c4d5e6f7a8b9c0d7", "payee": "64f1a2b3c4d5e6f7a8b9c0d6", "amount": 1500000, "currency": "INR", "paymentType": "advance", "milestone": "Interior Design Advance", "description": "Advance payment for office interior design work", "paymentMethod": "bank_transfer", "status": "completed", "transactionDate": "2024-01-30T15:20:00Z", "dueDate": "2024-01-30T00:00:00Z", "invoiceNumber": "INV-2024-007", "taxDetails": {"gst": {"rate": 18, "amount": 270000}, "totalTax": 270000}, "netAmount": 1230000, "grossAmount": 1500000, "paymentProof": {"receiptUrl": "https://example.com/receipts/payment_007.pdf", "bankReference": "RTGS123456789"}, "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d7", "createdAt": "2024-01-30T15:00:00Z", "updatedAt": "2024-01-30T15:20:00Z"}]