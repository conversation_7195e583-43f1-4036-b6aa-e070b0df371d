import { StyleSheet, Dimensions } from 'react-native';

const { height, width } = Dimensions.get('window');

export const styles = StyleSheet.create({
    // Container Styles
    container: {
        flex: 1,
    },
    scrollContainer: {
        flexGrow: 1,
        justifyContent: 'center',
    },
    backgroundContainer: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: height * 0.6,
        zIndex: -1,
    },
    backgroundImage: {
        width: '100%',
        height: '100%',
    },
    backgroundOverlay: {
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
    },
    contentContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: height * 0.1,
    },

    // Form Container Styles
    formContainer: {
        width: '90%',
        maxWidth: 400,
        borderRadius: 20,
        padding: 24,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    stepContainer: {
        width: '100%',
        paddingHorizontal: 16,
        paddingVertical: 20,
    },

    // Typography Styles
    title: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    subtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 12,
    },
    stepTitle: {
        fontSize: 22,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    stepSubtitle: {
        fontSize: 16,
        marginBottom: 24,
        textAlign: 'center',
        opacity: 0.8,
    },

    // Input Styles
    inputContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        paddingHorizontal: 12,
        height: 56,
    },
    multilineInputContainer: {
        height: 100,
        alignItems: 'flex-start',
        paddingVertical: 12,
    },
    addressInputContainer: {
        height: 80,
        alignItems: 'flex-start',
        paddingVertical: 12,
    },
    inputError: {
        borderColor: 'red',
    },
    errorText: {
        color: 'red',
        fontSize: 12,
        marginBottom: 8,
        marginLeft: 4,
    },
    inputIcon: {
        marginRight: 12,
    },
    input: {
        flex: 1,
        height: '100%',
        fontSize: 16,
    },
    multilineInput: {
        flex: 1,
        fontSize: 16,
        textAlignVertical: 'top',
    },
    disabledInputContainer: {
        opacity: 0.6,
    },
    disabledInput: {
        opacity: 0.6,
    },

    // Button Styles
    submitButton: {
        borderRadius: 12,
        overflow: 'hidden',
        borderWidth: 2,
        marginTop: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
        elevation: 4,
    },
    submitButtonGradient: {
        paddingVertical: 16,
        alignItems: 'center',
    },
    submitButtonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    buttonContainer: {
        flexDirection: 'row',
        marginTop: 16,
        gap: 12,
    },
    secondaryButton: {
        flex: 1,
        borderRadius: 12,
        borderWidth: 2,
        paddingVertical: 14,
        alignItems: 'center',
    },
    secondaryButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    primaryButton: {
        flex: 1,
        borderRadius: 12,
        overflow: 'hidden',
    },
    primaryButtonGradient: {
        paddingVertical: 14,
        alignItems: 'center',
    },
    primaryButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    disabledButton: {
        opacity: 0.5,
    },

    // Loading Styles
    loadingContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    loadingText: {
        marginLeft: 8,
        fontSize: 16,
        fontWeight: '600',
    },

    // File Upload Styles
    fileButton: {
        borderWidth: 1,
        borderRadius: 12,
        padding: 16,
        marginBottom: 16,
        alignItems: 'center',
        flexDirection: 'row',
    },
    fileButtonText: {
        fontSize: 16,
        flex: 1,
    },
    previewButton: {
        padding: 8,
    },
    uploadedFileContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 255, 0, 0.1)',
        borderRadius: 8,
        padding: 12,
        marginBottom: 8,
    },
    uploadedFileName: {
        flex: 1,
        fontSize: 14,
        fontWeight: '500',
    },

    // Picker Styles
    picker: {
        flex: 1,
        height: 56,
    },
    pickerContainer: {
        borderWidth: 1,
        borderRadius: 12,
        marginBottom: 8,
        height: 56,
        justifyContent: 'center',
    },

    // Date Picker Styles
    dateText: {
        flex: 1,
        fontSize: 16,
        paddingVertical: 15,
    },
    dateButton: {
        flexDirection: 'row',
        alignItems: 'center',
        borderWidth: 1,
        borderRadius: 12,
        paddingHorizontal: 12,
        height: 56,
        marginBottom: 8,
    },

    // Progress Indicator Styles
    progressContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        marginBottom: 24,
        paddingHorizontal: 20,
    },
    progressStep: {
        width: 30,
        height: 30,
        borderRadius: 15,
        alignItems: 'center',
        justifyContent: 'center',
        marginHorizontal: 4,
    },
    progressStepText: {
        fontSize: 12,
        fontWeight: 'bold',
    },
    progressLine: {
        flex: 1,
        height: 2,
        marginHorizontal: 4,
    },

    // Card Styles
    card: {
        borderRadius: 16,
        padding: 20,
        marginBottom: 16,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 8,
        elevation: 5,
    },
    cardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    cardTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    cardContent: {
        gap: 12,
    },

    // Tag Styles
    tagContainer: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 8,
        marginTop: 8,
    },
    tag: {
        flexDirection: 'row',
        alignItems: 'center',
        borderRadius: 16,
        borderWidth: 1,
        paddingHorizontal: 12,
        paddingVertical: 6,
    },
    tagText: {
        fontSize: 14,
        fontWeight: '500',
    },
    removeTagButton: {
        marginLeft: 6,
        padding: 2,
    },

    // Modal Styles
    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContent: {
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        width: width * 0.9,
        maxHeight: height * 0.8,
    },
    modalHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    modalTitle: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    closeButton: {
        padding: 8,
    },

    // Validation Styles
    validationContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginTop: 8,
        paddingHorizontal: 4,
    },
    validationText: {
        fontSize: 12,
        marginLeft: 6,
        fontWeight: '500',
    },
    validationSuccess: {
        color: '#10B981',
    },
    validationError: {
        color: '#EF4444',
    },

    // Checkbox Styles
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginVertical: 12,
    },
    checkboxLabel: {
        fontSize: 14,
        marginLeft: 8,
        flex: 1,
    },

    // Divider Styles
    divider: {
        height: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.1)',
        marginVertical: 16,
    },

    // Empty State Styles
    emptyContainer: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
        paddingVertical: 60,
        paddingHorizontal: 32,
    },
    emptyTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        marginTop: 16,
        marginBottom: 8,
        textAlign: 'center',
    },
    emptySubtitle: {
        fontSize: 16,
        textAlign: 'center',
        lineHeight: 24,
        opacity: 0.7,
    },
});

export default styles;
