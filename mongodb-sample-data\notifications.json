[{"_id": "64f1a2b3c4d5e6f7a8b9c8d1", "recipient": "64f1a2b3c4d5e6f7a8b9c0d1", "sender": "64f1a2b3c4d5e6f7a8b9c0d2", "type": "project_update", "title": "Foundation Work Completed", "message": "The foundation work for your villa construction project has been completed successfully. Structure work will begin next week.", "data": {"project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Foundation", "progress": 100}, "isRead": false, "priority": "high", "category": "project", "actionRequired": false, "createdAt": "2024-03-01T16:45:00Z", "readAt": null}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d2", "recipient": "64f1a2b3c4d5e6f7a8b9c0d1", "sender": "system", "type": "payment_reminder", "title": "Payment Due Reminder", "message": "Your payment of ₹11,50,000 for structure work milestone is due on March 20, 2024.", "data": {"payment": "64f1a2b3c4d5e6f7a8b9c7d5", "amount": 1150000, "dueDate": "2024-03-20T00:00:00Z"}, "isRead": false, "priority": "high", "category": "payment", "actionRequired": true, "createdAt": "2024-03-18T09:00:00Z", "readAt": null}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d3", "recipient": "64f1a2b3c4d5e6f7a8b9c0d4", "sender": "64f1a2b3c4d5e6f7a8b9c0d3", "type": "service_response", "title": "Property Search Response", "message": "I have found 5 properties matching your criteria in Hyderabad. Would you like to schedule site visits?", "data": {"serviceRequest": "64f1a2b3c4d5e6f7a8b9c3d2", "responseId": "response_001"}, "isRead": true, "priority": "medium", "category": "service", "actionRequired": true, "createdAt": "2024-01-27T10:30:00Z", "readAt": "2024-01-27T11:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d4", "recipient": "64f1a2b3c4d5e6f7a8b9c0d2", "sender": "64f1a2b3c4d5e6f7a8b9c0d7", "type": "new_project", "title": "New Project Assignment", "message": "You have been assigned to a new office complex construction project in Mumbai. Project value: ₹4.5 Crores.", "data": {"project": "64f1a2b3c4d5e6f7a8b9c2d2", "projectValue": 45000000}, "isRead": true, "priority": "high", "category": "project", "actionRequired": false, "createdAt": "2024-01-15T14:20:00Z", "readAt": "2024-01-15T15:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d5", "recipient": "64f1a2b3c4d5e6f7a8b9c0d3", "sender": "64f1a2b3c4d5e6f7a8b9c0d4", "type": "service_request", "title": "New Service Request", "message": "New property search request received for residential properties in Hyderabad. Budget: ₹50L - ₹1.5Cr.", "data": {"serviceRequest": "64f1a2b3c4d5e6f7a8b9c3d2", "budget": {"min": 5000000, "max": 15000000}}, "isRead": true, "priority": "medium", "category": "service", "actionRequired": true, "createdAt": "2024-01-26T17:00:00Z", "readAt": "2024-01-26T17:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d6", "recipient": "64f1a2b3c4d5e6f7a8b9c0d1", "sender": "system", "type": "document_verification", "title": "Document Verification Complete", "message": "Your property documents have been verified successfully by our site scout. Property is ready for listing.", "data": {"property": "64f1a2b3c4d5e6f7a8b9c1d1", "verifiedBy": "64f1a2b3c4d5e6f7a8b9c0d5"}, "isRead": true, "priority": "medium", "category": "verification", "actionRequired": false, "createdAt": "2024-01-20T11:00:00Z", "readAt": "2024-01-20T12:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d7", "recipient": "64f1a2b3c4d5e6f7a8b9c0d7", "sender": "64f1a2b3c4d5e6f7a8b9c0d6", "type": "service_response", "title": "Interior Design Proposal", "message": "I have prepared the interior design proposal for your office complex. The estimated cost is ₹65 lakhs.", "data": {"serviceRequest": "64f1a2b3c4d5e6f7a8b9c3d3", "quotedPrice": 6500000}, "isRead": false, "priority": "medium", "category": "service", "actionRequired": true, "createdAt": "2024-01-28T11:30:00Z", "readAt": null}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d8", "recipient": "64f1a2b3c4d5e6f7a8b9c0d9", "sender": "system", "type": "payment_success", "title": "Payment Successful", "message": "Your payment of ₹35,000 for property verification service has been processed successfully.", "data": {"payment": "64f1a2b3c4d5e6f7a8b9c7d6", "amount": 35000, "transactionId": "pay_AB12345682"}, "isRead": true, "priority": "low", "category": "payment", "actionRequired": false, "createdAt": "2024-02-10T12:35:00Z", "readAt": "2024-02-10T13:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c8d9", "recipient": "64f1a2b3c4d5e6f7a8b9c0d5", "sender": "64f1a2b3c4d5e6f7a8b9c0d9", "type": "service_request", "title": "Property Verification Request", "message": "New property verification request for commercial property in Mumbai. Urgent verification needed.", "data": {"serviceRequest": "64f1a2b3c4d5e6f7a8b9c3d4", "propertyType": "commercial", "location": "Mumbai"}, "isRead": false, "priority": "high", "category": "service", "actionRequired": true, "createdAt": "2024-01-28T16:00:00Z", "readAt": null}, {"_id": "64f1a2b3c4d5e6f7a8b9c8da", "recipient": "64f1a2b3c4d5e6f7a8b9c0d1", "sender": "system", "type": "milestone_achieved", "title": "Project Milestone Achieved", "message": "Congratulations! Your villa construction project has reached 45% completion. Structure work is progressing well.", "data": {"project": "64f1a2b3c4d5e6f7a8b9c2d1", "milestone": "45% Completion", "phase": "Structure"}, "isRead": false, "priority": "medium", "category": "project", "actionRequired": false, "createdAt": "2024-03-15T18:30:00Z", "readAt": null}]