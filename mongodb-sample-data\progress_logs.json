[{"_id": "64f1a2b3c4d5e6f7a8b9c6d1", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Foundation", "title": "Foundation Excavation Started", "description": "Excavation work for foundation has begun. Soil testing completed and foundation depth marked as per structural design.", "progress": 10, "date": "2024-02-01T09:00:00Z", "images": [{"url": "https://example.com/progress/excavation_start.jpg", "caption": "Excavation work in progress", "uploadedAt": "2024-02-01T16:00:00Z"}], "materials": [{"name": "Excavation Equipment", "quantity": "1 day rental", "cost": 8000}], "labor": {"workers": 8, "hours": 8, "cost": 6400}, "weather": "Clear", "issues": [], "nextSteps": "Complete excavation and prepare for concrete pouring", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-02-01T18:00:00Z", "updatedAt": "2024-02-01T18:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c6d2", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Foundation", "title": "Foundation Concrete Pouring", "description": "Foundation concrete pouring completed. Used M20 grade concrete as per specifications. Curing process initiated.", "progress": 60, "date": "2024-02-10T08:00:00Z", "images": [{"url": "https://example.com/progress/concrete_pouring.jpg", "caption": "Foundation concrete pouring", "uploadedAt": "2024-02-10T14:00:00Z"}, {"url": "https://example.com/progress/foundation_complete.jpg", "caption": "Foundation after concrete setting", "uploadedAt": "2024-02-10T17:00:00Z"}], "materials": [{"name": "M20 Concrete", "quantity": "45 cubic meters", "cost": 180000}, {"name": "Steel Reinforcement", "quantity": "2 tons", "cost": 130000}], "labor": {"workers": 12, "hours": 10, "cost": 12000}, "weather": "Partly cloudy", "issues": [{"description": "Minor delay due to concrete mixer breakdown", "resolution": "Arranged backup mixer, work resumed after 2 hours", "impact": "minimal"}], "nextSteps": "Continue curing for 7 days, then start column work", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-02-10T18:30:00Z", "updatedAt": "2024-02-10T18:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c6d3", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Foundation", "title": "Foundation Work Completed", "description": "Foundation work completed successfully. All quality checks passed. Ready to proceed with ground floor structure.", "progress": 100, "date": "2024-02-28T16:00:00Z", "images": [{"url": "https://example.com/progress/foundation_final.jpg", "caption": "Completed foundation ready for structure", "uploadedAt": "2024-02-28T16:30:00Z"}], "materials": [{"name": "Waterproofing Material", "quantity": "200 sq ft", "cost": 15000}], "labor": {"workers": 6, "hours": 8, "cost": 4800}, "weather": "Clear", "issues": [], "qualityChecks": [{"parameter": "Foundation Level", "status": "Passed", "checkedBy": "Site Engineer"}, {"parameter": "Concrete Strength", "status": "Passed", "checkedBy": "Quality Inspector"}], "nextSteps": "Start ground floor column and beam work", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-02-28T17:00:00Z", "updatedAt": "2024-02-28T17:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c6d4", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Structure", "title": "Ground Floor Column Work Started", "description": "Started ground floor column construction. Column reinforcement and formwork in progress.", "progress": 15, "date": "2024-03-05T09:00:00Z", "images": [{"url": "https://example.com/progress/column_reinforcement.jpg", "caption": "Column reinforcement work", "uploadedAt": "2024-03-05T15:00:00Z"}], "materials": [{"name": "Steel Bars (12mm, 16mm)", "quantity": "1.5 tons", "cost": 97500}, {"name": "Formwork Plywood", "quantity": "50 sheets", "cost": 25000}], "labor": {"workers": 10, "hours": 8, "cost": 8000}, "weather": "Clear", "issues": [], "nextSteps": "Complete column formwork and concrete pouring", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-03-05T17:30:00Z", "updatedAt": "2024-03-05T17:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c6d5", "project": "64f1a2b3c4d5e6f7a8b9c2d1", "phase": "Structure", "title": "Ground Floor Slab Completed", "description": "Ground floor slab concrete work completed. Beam and slab reinforcement done as per structural drawings.", "progress": 65, "date": "2024-03-15T14:00:00Z", "images": [{"url": "https://example.com/progress/ground_floor_slab.jpg", "caption": "Ground floor slab completed", "uploadedAt": "2024-03-15T16:00:00Z"}], "materials": [{"name": "M25 Concrete", "quantity": "35 cubic meters", "cost": 157500}, {"name": "Steel Reinforcement", "quantity": "2.5 tons", "cost": 162500}], "labor": {"workers": 15, "hours": 12, "cost": 18000}, "weather": "Partly cloudy", "issues": [], "qualityChecks": [{"parameter": "<PERSON><PERSON><PERSON>", "status": "Passed", "checkedBy": "Site Engineer"}, {"parameter": "Reinforcement Spacing", "status": "Passed", "checkedBy": "Structural Engineer"}], "nextSteps": "Start first floor column work after curing", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d1", "createdAt": "2024-03-15T18:00:00Z", "updatedAt": "2024-03-15T18:00:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c6d6", "project": "64f1a2b3c4d5e6f7a8b9c2d2", "phase": "Foundation", "title": "Office Complex Foundation Started", "description": "Foundation work for 5-floor office complex initiated. Deep foundation with pile work in progress.", "progress": 20, "date": "2024-02-01T08:00:00Z", "images": [{"url": "https://example.com/progress/office_foundation.jpg", "caption": "Office complex foundation work", "uploadedAt": "2024-02-01T17:00:00Z"}], "materials": [{"name": "<PERSON><PERSON><PERSON>", "quantity": "50 pieces", "cost": 500000}, {"name": "Steel Reinforcement", "quantity": "8 tons", "cost": 520000}], "labor": {"workers": 25, "hours": 10, "cost": 25000}, "weather": "Clear", "issues": [{"description": "Hard rock layer encountered at 15 feet", "resolution": "Used hydraulic rock breaker", "impact": "2 days delay"}], "nextSteps": "Complete pile foundation and start grade beam work", "loggedBy": "64f1a2b3c4d5e6f7a8b9c0d2", "approvedBy": "64f1a2b3c4d5e6f7a8b9c0d7", "createdAt": "2024-02-01T19:00:00Z", "updatedAt": "2024-02-01T19:00:00Z"}]