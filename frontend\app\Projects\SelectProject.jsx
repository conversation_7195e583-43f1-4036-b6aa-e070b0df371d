import React, { useContext, useState, useRef } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StatusBar,
    SafeAreaView,
    RefreshControl,
    Animated,
    Alert,
} from 'react-native';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { ThemeContext } from '../../context/ThemeContext';
import BackButton from '../Components/Shared/BackButton';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchUserProjects } from '../../api/projects/projectApi';
import { sendServiceRequest } from '../../api/serviceRequests/serviceRequestApi';
import { showToast } from '../../utils/showToast';
import { styles } from './styles';

export default function SelectProject() {
    const { theme, isDarkMode } = useContext(ThemeContext);
    const router = useRouter();
    const { recipientId, recipientName, recipientType } =
        useLocalSearchParams();
    const queryClient = useQueryClient();
    const [selectedProject, setSelectedProject] = useState(null);
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;


    const {
        data: projectsData,
        isLoading,
        error,
        refetch,
        isRefetching,
    } = useQuery({
        queryKey: ['userProjects'],
        queryFn: fetchUserProjects,
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to fetch projects'
            );
        },
    });

    React.useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                useNativeDriver: true,
            }),
        ]).start();
    }, []);

    const sendRequestMutation = useMutation({
        mutationFn: sendServiceRequest,
        onSuccess: () => {
            showToast(
                'success',
                'Success',
                'Service request sent successfully'
            );
            queryClient.invalidateQueries(['serviceRequests']);
            router.back();
        },
        onError: (error) => {
            showToast(
                'error',
                'Error',
                error.message || 'Failed to send service request'
            );
        },
    });

    const projects = projectsData?.projects || [];



    const handleSelectProject = (project) => {
        Alert.alert(
            'Send Service Request',
            `Send service request to ${recipientName} for "${project.projectName}"?`,
            [
                { text: 'Cancel', style: 'cancel' },
                {
                    text: 'Send Request',
                    onPress: () => {
                        sendRequestMutation.mutate({
                            recipientId: recipientId,
                            recipientType: recipientType,
                            projectId: project._id,
                        });
                    },
                },
            ]
        );
    };

    const formatBudget = (budget) => {
        if (!budget || (!budget.minBudget && !budget.maxBudget))
            return 'Budget not specified';

        const formatAmount = (amount) => {
            if (amount >= 10000000)
                return `₹${(amount / 10000000).toFixed(1)}Cr`;
            if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
            if (amount >= 1000) return `₹${(amount / 1000).toFixed(1)}K`;
            return `₹${amount}`;
        };

        if (budget.minBudget && budget.maxBudget) {
            return `${formatAmount(budget.minBudget)} - ${formatAmount(budget.maxBudget)}`;
        }
        if (budget.minBudget) return `From ${formatAmount(budget.minBudget)}`;
        if (budget.maxBudget) return `Up to ${formatAmount(budget.maxBudget)}`;
        return 'Budget not specified';
    };

    const ProjectCard = ({ project }) => {
        const canHire =
            recipientType === 'broker'
                ? !project.brokerId
                : recipientType === 'contractor'
                    ? (!project.brokerAssistanceRequired || project.brokerId) &&
                    !project.contractorId
                    : true;

        return (
            <TouchableOpacity
                style={[
                    styles.projectCard,
                    {
                        backgroundColor: theme.CARD,
                        opacity: canHire ? 1 : 0.6,
                    },
                ]}
                onPress={() => canHire && handleSelectProject(project)}
                activeOpacity={0.8}
                disabled={!canHire || sendRequestMutation.isLoading}
            >
                <View style={styles.cardHeader}>
                    <View style={styles.projectInfo}>
                        <Text
                            style={[
                                styles.projectName,
                                { color: theme.TEXT_PRIMARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.projectName}
                        </Text>
                        <Text
                            style={[
                                styles.projectType,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {project.projectType || 'Not specified'} • {project.constructionType || 'Not specified'}
                        </Text>
                    </View>
                    {!canHire && (
                        <View
                            style={[
                                styles.statusBadge,
                                { backgroundColor: theme.ERROR + '20' },
                            ]}
                        >
                            <Text
                                style={[
                                    styles.statusText,
                                    { color: theme.ERROR },
                                ]}
                            >
                                {recipientType === 'contractor' &&
                                    project.brokerAssistanceRequired &&
                                    !project.brokerId
                                    ? 'Broker Required'
                                    : 'Already Hired'}
                            </Text>
                        </View>
                    )}
                </View>

                <View style={styles.cardContent}>
                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="location-on"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                            numberOfLines={1}
                        >
                            {project.location?.city}, {project.location?.state}
                        </Text>
                    </View>

                    <View style={styles.detailRow}>
                        <MaterialIcons
                            name="attach-money"
                            size={16}
                            color={theme.TEXT_SECONDARY}
                        />
                        <Text
                            style={[
                                styles.detailText,
                                { color: theme.TEXT_SECONDARY },
                            ]}
                        >
                            {formatBudget(project.budget)}
                        </Text>
                    </View>
                </View>

                {canHire && (
                    <View style={styles.cardFooter}>
                        <LinearGradient
                            colors={[theme.PRIMARY, theme.SECONDARY]}
                            start={{ x: 0, y: 0 }}
                            end={{ x: 1, y: 1 }}
                            style={styles.selectButton}
                        >
                            <Text
                                style={[
                                    styles.selectButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                {sendRequestMutation.isLoading
                                    ? 'Sending...'
                                    : 'Select Project'}
                            </Text>
                        </LinearGradient>
                    </View>
                )}
            </TouchableOpacity>
        );
    };

    const EmptyState = () => (
        <View style={styles.emptyContainer}>
            <MaterialIcons
                name="assignment"
                size={80}
                color={theme.TEXT_SECONDARY}
            />
            <Text style={[styles.emptyTitle, { color: theme.TEXT_PRIMARY }]}>
                No Projects Available
            </Text>
            <Text
                style={[styles.emptySubtitle, { color: theme.TEXT_SECONDARY }]}
            >
                Create a project first to send service requests to professionals
            </Text>
            <TouchableOpacity
                style={styles.createButton}
                onPress={() => router.push('/Projects/CreateProject')}
                activeOpacity={0.8}
            >
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 1 }}
                    style={styles.gradient}
                />
                <Text style={[styles.createButtonText, { color: theme.WHITE }]}>
                    Create Project
                </Text>
            </TouchableOpacity>
        </View>
    );

    return (
        <View style={[combinedStyles.container, { backgroundColor: theme.BACKGROUND }]}>
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            {/* Background Gradient */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                start={{ x: 0, y: 0.2 }}
                end={{ x: 0.9, y: 0.4 }}
                style={combinedStyles.backgroundContainer}
            />

            <BackButton color={theme.WHITE} />

            {/* Header */}
            <View style={[combinedStyles.header, { backgroundColor: 'transparent' }]}>
                <Text
                    style={[combinedStyles.headerTitle, { color: theme.WHITE }]}
                    numberOfLines={2}
                >
                    Select Project for {recipientName}
                </Text>
                <Text style={[combinedStyles.subtitle, { color: theme.WHITE + 'CC' }]}>
                    Choose a project to send service request
                </Text>
            </View>

            <ScrollView
                style={combinedStyles.scrollContainer}
                contentContainerStyle={combinedStyles.scrollContent}
                showsVerticalScrollIndicator={false}
                refreshControl={
                    <RefreshControl
                        refreshing={isRefetching}
                        onRefresh={refetch}
                        colors={[theme.PRIMARY]}
                        tintColor={theme.WHITE}
                    />
                }
            >
                <Animated.View
                    style={[
                        combinedStyles.contentContainer,
                        {
                            opacity: fadeAnim,
                            transform: [{ translateY: slideAnim }],
                        },
                    ]}
                >
                    {isLoading ? (
                        <View style={combinedStyles.loadingContainer}>
                            <Text
                                style={[
                                    combinedStyles.loadingText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Loading projects...
                            </Text>
                        </View>
                    ) : projects.length === 0 ? (
                        <EmptyState />
                    ) : (
                        <View style={combinedStyles.projectsList}>
                            <Text
                                style={[
                                    combinedStyles.instructionText,
                                    { color: theme.TEXT_SECONDARY },
                                ]}
                            >
                                Select a project to send service request to{' '}
                                {recipientName}
                            </Text>
                            {projects.map((project, index) => (
                                <ProjectCard
                                    key={project._id}
                                    project={project}
                                    index={index}
                                />
                            ))}
                        </View>
                    )}
                </Animated.View>
            </ScrollView>
        </View>
    );
}

// Additional styles specific to SelectProject
const localStyles = {
    header: {
        paddingHorizontal: 16,
        paddingVertical: 16,
        marginTop: 20,
        alignItems: 'center',
    },
    headerTitle: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 8,
        textAlign: 'center',
    },
    scrollContent: {
        paddingVertical: 12,
        paddingHorizontal: 16,
    },
    projectsList: {
        gap: 16,
    },
    instructionText: {
        fontSize: 14,
        marginBottom: 16,
        textAlign: 'center',
        fontStyle: 'italic',
    },
    projectInfo: {
        flex: 1,
        marginRight: 12,
    },
    detailRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 8,
        marginBottom: 8,
    },
    detailText: {
        fontSize: 14,
        flex: 1,
    },
    cardFooter: {
        paddingTop: 12,
        borderTopWidth: 1,
        borderTopColor: 'rgba(0,0,0,0.1)',
    },
    selectButton: {
        borderRadius: 8,
        paddingVertical: 12,
        alignItems: 'center',
    },
    selectButtonText: {
        fontSize: 16,
        fontWeight: '600',
    },
    createButton: {
        borderRadius: 12,
        overflow: 'hidden',
        elevation: 4,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 8,
    },
    gradient: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
    },
    createButtonText: {
        fontSize: 16,
        fontWeight: '600',
        textAlign: 'center',
        paddingVertical: 16,
        paddingHorizontal: 32,
    },
};

// Merge imported styles with local styles
const combinedStyles = { ...styles, ...localStyles };
