[{"_id": "64f1a2b3c4d5e6f7a8b9c0d1", "name": "<PERSON><PERSON>", "email": "raj<PERSON>.<EMAIL>", "phone": "+91-9876543210", "password": "$2b$10$hashedpassword1", "role": "property_owner", "profileImage": "https://example.com/profiles/rajesh.jpg", "address": {"street": "123 MG Road", "city": "Bangalore", "state": "Karnataka", "pincode": "560001", "coordinates": {"latitude": 12.9716, "longitude": 77.5946}}, "documents": {"aadhaar": {"number": "1234-5678-9012", "verified": true, "documentUrl": "https://example.com/docs/aadhaar1.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan1.pdf"}}, "isActive": true, "createdAt": "2024-01-15T10:30:00Z", "updatedAt": "2024-01-15T10:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d2", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-9876543211", "password": "$2b$10$hashedpassword2", "role": "contractor", "profileImage": "https://example.com/profiles/priya.jpg", "specialization": ["residential", "commercial"], "experience": 8, "rating": 4.7, "completedProjects": 45, "address": {"street": "456 Brigade Road", "city": "Bangalore", "state": "Karnataka", "pincode": "560025", "coordinates": {"latitude": 12.9698, "longitude": 77.6205}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar2.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan2.pdf"}, "license": {"number": "CON123456", "verified": true, "documentUrl": "https://example.com/docs/license2.pdf"}}, "portfolio": [{"projectName": "Luxury Villa Construction", "imageUrl": "https://example.com/portfolio/villa1.jpg", "description": "3BHK luxury villa with modern amenities"}], "isActive": true, "createdAt": "2024-01-16T09:15:00Z", "updatedAt": "2024-01-16T09:15:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d3", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-**********", "password": "$2b$10$hashedpassword3", "role": "broker", "profileImage": "https://example.com/profiles/amit.jpg", "licenseNumber": "BRK789012", "experience": 12, "rating": 4.5, "dealsCompleted": 78, "commission": 2.5, "address": {"street": "789 Commercial Street", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "coordinates": {"latitude": 19.076, "longitude": 72.8777}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar3.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan3.pdf"}, "brokerLicense": {"number": "BRK789012", "verified": true, "documentUrl": "https://example.com/docs/broker3.pdf"}}, "isActive": true, "createdAt": "2024-01-17T11:45:00Z", "updatedAt": "2024-01-17T11:45:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d4", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-**********", "password": "$2b$10$hashedpassword4", "role": "buyer", "profileImage": "https://example.com/profiles/sunita.jpg", "budget": {"min": 5000000, "max": 15000000}, "preferences": {"propertyType": ["residential", "commercial"], "location": ["Hyderabad", "Bangalore"], "amenities": ["parking", "security", "garden"]}, "address": {"street": "321 Jubilee Hills", "city": "Hyderabad", "state": "Telangana", "pincode": "500033", "coordinates": {"latitude": 17.4065, "longitude": 78.4772}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar4.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan4.pdf"}}, "isActive": true, "createdAt": "2024-01-18T14:20:00Z", "updatedAt": "2024-01-18T14:20:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d5", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-9876543214", "password": "$2b$10$hashedpassword5", "role": "site_scout", "profileImage": "https://example.com/profiles/vikram.jpg", "experience": 6, "rating": 4.3, "areasOfExpertise": ["residential", "agricultural", "commercial"], "verifiedProperties": 156, "address": {"street": "654 Sector 14", "city": "Gurgaon", "state": "Haryana", "pincode": "122001", "coordinates": {"latitude": 28.4595, "longitude": 77.0266}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar5.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan5.pdf"}}, "isActive": true, "createdAt": "2024-01-19T16:10:00Z", "updatedAt": "2024-01-19T16:10:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d6", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-9876543215", "password": "$2b$10$hashedpassword6", "role": "contractor", "profileImage": "https://example.com/profiles/meera.jpg", "specialization": ["interior", "renovation"], "experience": 5, "rating": 4.6, "completedProjects": 32, "address": {"street": "987 Anna Nagar", "city": "Chennai", "state": "Tamil Nadu", "pincode": "600040", "coordinates": {"latitude": 13.0827, "longitude": 80.2707}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar6.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan6.pdf"}}, "isActive": true, "createdAt": "2024-01-20T08:30:00Z", "updatedAt": "2024-01-20T08:30:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d7", "name": "<PERSON>", "email": "<EMAIL>", "phone": "+91-9876543216", "password": "$2b$10$hashedpassword7", "role": "property_owner", "profileImage": "https://example.com/profiles/ravi.jpg", "address": {"street": "147 Civil Lines", "city": "Delhi", "state": "Delhi", "pincode": "110054", "coordinates": {"latitude": 28.7041, "longitude": 77.1025}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar7.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan7.pdf"}}, "isActive": true, "createdAt": "2024-01-21T12:15:00Z", "updatedAt": "2024-01-21T12:15:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d8", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-**********", "password": "$2b$10$hashedpassword8", "role": "broker", "profileImage": "https://example.com/profiles/kavya.jpg", "licenseNumber": "BRK345678", "experience": 7, "rating": 4.8, "dealsCompleted": 52, "commission": 2.0, "address": {"street": "258 Marine Drive", "city": "<PERSON><PERSON>", "state": "Kerala", "pincode": "682031", "coordinates": {"latitude": 9.9312, "longitude": 76.2673}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar8.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan8.pdf"}}, "isActive": true, "createdAt": "2024-01-22T15:45:00Z", "updatedAt": "2024-01-22T15:45:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0d9", "name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-**********", "password": "$2b$10$hashedpassword9", "role": "buyer", "profileImage": "https://example.com/profiles/arjun.jpg", "budget": {"min": 8000000, "max": 25000000}, "preferences": {"propertyType": ["luxury", "commercial"], "location": ["Mumbai", "Pune"], "amenities": ["gym", "pool", "clubhouse"]}, "address": {"street": "369 Bandra West", "city": "Mumbai", "state": "Maharashtra", "pincode": "400050", "coordinates": {"latitude": 19.0596, "longitude": 72.8295}}, "documents": {"aadhaar": {"number": "**************", "verified": true, "documentUrl": "https://example.com/docs/aadhaar9.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan9.pdf"}}, "isActive": true, "createdAt": "2024-01-23T10:20:00Z", "updatedAt": "2024-01-23T10:20:00Z"}, {"_id": "64f1a2b3c4d5e6f7a8b9c0da", "name": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+91-9876543219", "password": "$2b$10$hashedpassword10", "role": "site_scout", "profileImage": "https://example.com/profiles/deepika.jpg", "experience": 4, "rating": 4.4, "areasOfExpertise": ["residential", "industrial"], "verifiedProperties": 89, "address": {"street": "741 Vaishali Nagar", "city": "Jaipur", "state": "Rajasthan", "pincode": "302021", "coordinates": {"latitude": 26.9124, "longitude": 75.7873}}, "documents": {"aadhaar": {"number": "0123-4567-8901", "verified": true, "documentUrl": "https://example.com/docs/aadhaar10.pdf"}, "pan": {"number": "**********", "verified": true, "documentUrl": "https://example.com/docs/pan10.pdf"}}, "isActive": true, "createdAt": "2024-01-24T13:55:00Z", "updatedAt": "2024-01-24T13:55:00Z"}]